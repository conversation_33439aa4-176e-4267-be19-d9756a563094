package org.example123.iosapp.client;

/**
 * Data class for player coordinates
 */
public class PlayerCoordinates {
    private double x;
    private double y;
    private double z;
    private String dimension;
    private long timestamp;
    private String playerName;

    public PlayerCoordinates(double x, double y, double z, String dimension, String playerName) {
        this.x = x;
        this.y = y;
        this.z = z;
        this.dimension = dimension;
        this.playerName = playerName;
        this.timestamp = System.currentTimeMillis();
    }

    // Getters
    public double getX() { return x; }
    public double getY() { return y; }
    public double getZ() { return z; }
    public String getDimension() { return dimension; }
    public long getTimestamp() { return timestamp; }
    public String getPlayerName() { return playerName; }

    // Setters
    public void setX(double x) { this.x = x; }
    public void setY(double y) { this.y = y; }
    public void setZ(double z) { this.z = z; }
    public void setDimension(String dimension) { this.dimension = dimension; }
    public void setPlayerName(String playerName) { this.playerName = playerName; }
    public void updateTimestamp() { this.timestamp = System.currentTimeMillis(); }

    @Override
    public String toString() {
        return String.format("PlayerCoordinates{x=%.2f, y=%.2f, z=%.2f, dimension='%s', player='%s', timestamp=%d}",
                x, y, z, dimension, playerName, timestamp);
    }
}
