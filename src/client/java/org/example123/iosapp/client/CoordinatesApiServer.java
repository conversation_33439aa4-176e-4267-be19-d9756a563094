package org.example123.iosapp.client;

import com.google.gson.Gson;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;

import java.io.IOException;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.Executors;

/**
 * HTTP Server for providing player coordinates API
 */
public class CoordinatesApiServer {
    private static final int DEFAULT_PORT = 8080;
    private HttpServer server;
    private final Gson gson = new Gson();
    private volatile PlayerCoordinates lastCoordinates;

    public CoordinatesApiServer() {
        this(DEFAULT_PORT);
    }

    public CoordinatesApiServer(int port) {
        try {
            server = HttpServer.create(new InetSocketAddress(port), 0);
            setupEndpoints();
            server.setExecutor(Executors.newFixedThreadPool(4));
            System.out.println("[IosApp] API Server initialized on port " + port);
        } catch (IOException e) {
            System.err.println("[IosApp] Failed to create HTTP server: " + e.getMessage());
        }
    }

    private void setupEndpoints() {
        // Main coordinates endpoint
        server.createContext("/coords", new CoordinatesHandler());
        
        // Health check endpoint
        server.createContext("/health", new HealthHandler());
        
        // Info endpoint
        server.createContext("/info", new InfoHandler());
    }

    public void start() {
        if (server != null) {
            server.start();
            System.out.println("[IosApp] API Server started successfully");
        }
    }

    public void stop() {
        if (server != null) {
            server.stop(0);
            System.out.println("[IosApp] API Server stopped");
        }
    }

    public void updateCoordinates(PlayerCoordinates coordinates) {
        this.lastCoordinates = coordinates;
    }

    // Handler for /coords endpoint
    private class CoordinatesHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            // Enable CORS for web requests
            exchange.getResponseHeaders().add("Access-Control-Allow-Origin", "*");
            exchange.getResponseHeaders().add("Access-Control-Allow-Methods", "GET, OPTIONS");
            exchange.getResponseHeaders().add("Access-Control-Allow-Headers", "Content-Type");
            exchange.getResponseHeaders().add("Content-Type", "application/json");

            if ("OPTIONS".equals(exchange.getRequestMethod())) {
                exchange.sendResponseHeaders(200, 0);
                exchange.close();
                return;
            }

            if ("GET".equals(exchange.getRequestMethod())) {
                String response;
                int statusCode;

                if (lastCoordinates != null) {
                    response = gson.toJson(lastCoordinates);
                    statusCode = 200;
                } else {
                    response = "{\"error\":\"No coordinates available\",\"message\":\"Player coordinates not yet tracked\"}";
                    statusCode = 404;
                }

                byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
                exchange.sendResponseHeaders(statusCode, responseBytes.length);
                
                try (OutputStream os = exchange.getResponseBody()) {
                    os.write(responseBytes);
                }
            } else {
                String response = "{\"error\":\"Method not allowed\",\"allowed\":[\"GET\"]}";
                byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
                exchange.sendResponseHeaders(405, responseBytes.length);
                
                try (OutputStream os = exchange.getResponseBody()) {
                    os.write(responseBytes);
                }
            }
        }
    }

    // Handler for /health endpoint
    private class HealthHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            exchange.getResponseHeaders().add("Access-Control-Allow-Origin", "*");
            exchange.getResponseHeaders().add("Content-Type", "application/json");

            if ("GET".equals(exchange.getRequestMethod())) {
                String response = "{\"status\":\"healthy\",\"timestamp\":" + System.currentTimeMillis() + "}";
                byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
                exchange.sendResponseHeaders(200, responseBytes.length);
                
                try (OutputStream os = exchange.getResponseBody()) {
                    os.write(responseBytes);
                }
            } else {
                exchange.sendResponseHeaders(405, 0);
            }
            exchange.close();
        }
    }

    // Handler for /info endpoint
    private class InfoHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            exchange.getResponseHeaders().add("Access-Control-Allow-Origin", "*");
            exchange.getResponseHeaders().add("Content-Type", "application/json");

            if ("GET".equals(exchange.getRequestMethod())) {
                String response = "{\"mod\":\"IosApp Coordinates Tracker\",\"version\":\"1.0\",\"endpoints\":[\"/coords\",\"/health\",\"/info\"]}";
                byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
                exchange.sendResponseHeaders(200, responseBytes.length);
                
                try (OutputStream os = exchange.getResponseBody()) {
                    os.write(responseBytes);
                }
            } else {
                exchange.sendResponseHeaders(405, 0);
            }
            exchange.close();
        }
    }
}
