package org.example123.iosapp.client;

import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.registry.RegistryKey;
import net.minecraft.world.World;

/**
 * Tracks player coordinates and updates the API server
 */
public class PlayerTracker {
    private final CoordinatesApiServer apiServer;
    private PlayerCoordinates lastCoordinates;
    private int tickCounter = 0;
    private static final int UPDATE_INTERVAL = 10; // Update every 10 ticks (0.5 seconds)

    public PlayerTracker(CoordinatesApiServer apiServer) {
        this.apiServer = apiServer;
        registerTickHandler();
    }

    private void registerTickHandler() {
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            tickCounter++;
            
            // Update coordinates every UPDATE_INTERVAL ticks to avoid spam
            if (tickCounter >= UPDATE_INTERVAL) {
                tickCounter = 0;
                updatePlayerCoordinates(client);
            }
        });
    }

    private void updatePlayerCoordinates(MinecraftClient client) {
        ClientPlayerEntity player = client.player;
        
        if (player == null || client.world == null) {
            return;
        }

        // Get current position
        BlockPos pos = player.getBlockPos();
        double x = player.getX();
        double y = player.getY();
        double z = player.getZ();

        // Get dimension name
        String dimensionName = getDimensionName(client.world.getRegistryKey());
        
        // Get player name
        String playerName = player.getName().getString();

        // Check if coordinates have changed significantly (to reduce unnecessary updates)
        if (hasCoordinatesChanged(x, y, z, dimensionName)) {
            PlayerCoordinates newCoordinates = new PlayerCoordinates(x, y, z, dimensionName, playerName);
            lastCoordinates = newCoordinates;
            
            // Update API server
            apiServer.updateCoordinates(newCoordinates);
            
            // Optional: Log coordinates for debugging
            // System.out.println("[IosApp] Updated coordinates: " + newCoordinates);
        }
    }

    private boolean hasCoordinatesChanged(double x, double y, double z, String dimension) {
        if (lastCoordinates == null) {
            return true;
        }

        // Check if position changed by at least 0.5 blocks or dimension changed
        double threshold = 0.5;
        return Math.abs(lastCoordinates.getX() - x) > threshold ||
               Math.abs(lastCoordinates.getY() - y) > threshold ||
               Math.abs(lastCoordinates.getZ() - z) > threshold ||
               !lastCoordinates.getDimension().equals(dimension);
    }

    private String getDimensionName(RegistryKey<World> worldKey) {
        String dimensionId = worldKey.getValue().toString();
        
        // Convert dimension IDs to readable names
        switch (dimensionId) {
            case "minecraft:overworld":
                return "Overworld";
            case "minecraft:the_nether":
                return "Nether";
            case "minecraft:the_end":
                return "End";
            default:
                // For modded dimensions, return the ID
                return dimensionId;
        }
    }

    public PlayerCoordinates getLastCoordinates() {
        return lastCoordinates;
    }
}
