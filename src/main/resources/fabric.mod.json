{"schemaVersion": 1, "id": "iosapp", "version": "${version}", "name": "iOS App Coordinates Tracker", "description": "Minecraft mod that provides player coordinates via HTTP API for iOS app integration", "authors": [], "contact": {}, "license": "All-Rights-Reserved", "icon": "assets/iosapp/icon.png", "environment": "client", "entrypoints": {"fabric-datagen": ["org.example123.iosapp.client.IosappDataGenerator"], "client": ["org.example123.iosapp.client.IosappClient"], "main": ["org.example123.iosapp.Iosapp"]}, "mixins": ["iosapp.mixins.json", {"config": "iosapp.client.mixins.json", "environment": "client"}], "depends": {"fabricloader": ">=${loader_version}", "fabric": "*", "minecraft": "${minecraft_version}"}}