# iOS App Coordinates Tracker - Minecraft Mod

Этот мод для Minecraft (Fabric) предоставляет HTTP API для получения координат игрока в реальном времени. Предназначен для интеграции с iOS приложением.

## Возможности

- 🎯 Отслеживание координат игрока в реальном времени
- 🌐 HTTP API сервер на порту 8080
- 📱 CORS поддержка для веб/мобильных приложений
- 🗺️ Поддержка всех измерений (Overworld, Nether, End, модовые)
- ⚡ Оптимизированное обновление (каждые 0.5 секунды)
- 🔍 Автоматическое определение изменений координат

## API Эндпоинты

### GET `/coords`
Возвращает текущие координаты игрока в формате JSON:

```json
{
  "x": 123.45,
  "y": 64.0,
  "z": -67.89,
  "dimension": "Overworld",
  "timestamp": 1640995200000,
  "playerName": "Player"
}
```

### GET `/health`
Проверка состояния сервера:

```json
{
  "status": "healthy",
  "timestamp": 1640995200000
}
```

### GET `/info`
Информация о моде:

```json
{
  "mod": "IosApp Coordinates Tracker",
  "version": "1.0",
  "endpoints": ["/coords", "/health", "/info"]
}
```

## Установка

1. Убедитесь, что у вас установлен Fabric Loader для Minecraft 1.21.1
2. Скомпилируйте мод: `./gradlew build`
3. Скопируйте `.jar` файл из `build/libs/` в папку `mods` вашего Minecraft
4. Запустите Minecraft

## Использование

1. Запустите Minecraft с установленным модом
2. Войдите в мир
3. API сервер автоматически запустится на `http://localhost:8080`
4. Ваше iOS приложение может обращаться к `http://[IP_КОМПЬЮТЕРА]:8080/coords`

## Для разработчиков iOS

### Пример Swift кода для получения координат:

```swift
import Foundation

struct PlayerCoordinates: Codable {
    let x: Double
    let y: Double
    let z: Double
    let dimension: String
    let timestamp: Int64
    let playerName: String
}

class MinecraftAPI {
    private let baseURL: String
    
    init(computerIP: String, port: Int = 8080) {
        self.baseURL = "http://\(computerIP):\(port)"
    }
    
    func fetchCoordinates(completion: @escaping (PlayerCoordinates?) -> Void) {
        guard let url = URL(string: "\(baseURL)/coords") else {
            completion(nil)
            return
        }
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            guard let data = data,
                  let coordinates = try? JSONDecoder().decode(PlayerCoordinates.self, from: data) else {
                completion(nil)
                return
            }
            
            DispatchQueue.main.async {
                completion(coordinates)
            }
        }.resume()
    }
    
    func startPeriodicUpdates(interval: TimeInterval = 1.0, 
                            onUpdate: @escaping (PlayerCoordinates?) -> Void) {
        Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { _ in
            self.fetchCoordinates(completion: onUpdate)
        }
    }
}

// Использование:
let api = MinecraftAPI(computerIP: "*************")
api.startPeriodicUpdates { coordinates in
    if let coords = coordinates {
        print("Player at: \(coords.x), \(coords.y), \(coords.z)")
    }
}
```

## Настройка сети

1. **Убедитесь, что компьютер и iOS устройство в одной Wi-Fi сети**
2. **Узнайте IP адрес компьютера:**
   - Windows: `ipconfig`
   - macOS/Linux: `ifconfig` или `ip addr`
3. **Проверьте доступность API:** откройте в браузере `http://[IP]:8080/health`

## Требования

- Minecraft 1.21.1
- Fabric Loader 0.16.14+
- Fabric API 0.116.4+
- Java 21+

## Безопасность

⚠️ **Важно:** Этот мод открывает HTTP сервер на вашем компьютере. Используйте только в доверенных сетях!

## Лицензия

All Rights Reserved

## Поддержка

При возникновении проблем проверьте:
1. Логи Minecraft на наличие ошибок
2. Доступность порта 8080
3. Настройки брандмауэра
4. Подключение к одной сети Wi-Fi
